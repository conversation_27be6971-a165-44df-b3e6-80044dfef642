_wandb:
    value:
        cli_version: 0.21.0
        e:
            xgz5fmj6d9qcb1psjs1pr5zvo6ma9kzd:
                codePath: example.py
                codePathLocal: example.py
                cpu_count: 8
                cpu_count_logical: 16
                disk:
                    /:
                        total: "499308818432"
                        used: "296409407488"
                email: <EMAIL>
                executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe
                host: HHPC
                memory:
                    total: "34282369024"
                os: Windows-10-10.0.26100-SP0
                program: C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\example.py
                python: CPython 3.9.12
                root: C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym
                startedAt: "2025-07-21T04:38:39.127509Z"
                writerId: xgz5fmj6d9qcb1psjs1pr5zvo6ma9kzd
        m: []
        python_version: 3.9.12
        t:
            "1":
                - 1
            "2":
                - 1
            "3":
                - 2
                - 13
                - 16
            "4": 3.9.12
            "5": 0.21.0
            "8":
                - 3
            "10":
                - 20
            "12": 0.21.0
            "13": windows-amd64
critic_layer_sizes:
    value:
        - 2048
        - 1024
        - 1024
        - 1024
critic_lr:
    value: 0.0002
exp_buffer_size:
    value: 150000
gae_gamma:
    value: 0.99
gae_lambda:
    value: 0.95
min_inference_size:
    value: 14
n_proc:
    value: 16
policy_layer_sizes:
    value:
        - 2048
        - 1024
        - 1024
        - 1024
policy_lr:
    value: 0.0002
ppo_batch_size:
    value: 50000
ppo_clip_range:
    value: 0.1
ppo_ent_coef:
    value: 0.01
ppo_epochs:
    value: 2
ppo_minibatch_size:
    value: 25000
shm_buffer_size:
    value: 8192
standardize_obs:
    value: false
standardize_returns:
    value: true
timestep_limit:
    value: 100000000000
ts_per_iteration:
    value: 50000
