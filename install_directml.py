"""
DirectML Installation Helper Script

This script helps install a compatible version of torch-directml for your system.
"""

import subprocess
import sys
import platform


def run_command(command):
    """Run a command and return the result."""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)


def check_system_requirements():
    """Check if the system meets DirectML requirements."""
    print("=== Checking System Requirements ===")
    
    # Check Windows version
    if platform.system() != "Windows":
        print("❌ DirectML requires Windows")
        return False
    
    win_version = platform.win32_ver()
    print(f"✓ Windows version: {win_version[0]} {win_version[1]}")
    
    # Check Python version
    python_version = sys.version_info
    print(f"✓ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("❌ Python 3.7+ required")
        return False
    
    return True


def get_torch_version():
    """Get the current PyTorch version."""
    try:
        import torch
        return torch.__version__
    except ImportError:
        return None


def install_directml():
    """Install DirectML with version compatibility handling."""
    print("\n=== Installing DirectML ===")
    
    torch_version = get_torch_version()
    if torch_version:
        print(f"Current PyTorch version: {torch_version}")
    else:
        print("PyTorch not found - installing base requirements first")
        success, stdout, stderr = run_command("pip install torch")
        if not success:
            print(f"❌ Failed to install PyTorch: {stderr}")
            return False
        torch_version = get_torch_version()
    
    # Try different DirectML versions in order of preference
    directml_versions = [
        "torch-directml==0.2.3.dev240529",  # Known stable version
        "torch-directml==0.2.2.dev240124",  # Older stable version
        "torch-directml",                   # Latest version (might be unstable)
    ]
    
    for version in directml_versions:
        print(f"\nTrying to install {version}...")
        
        # Uninstall any existing version first
        run_command("pip uninstall torch-directml -y")
        
        # Install the version
        success, stdout, stderr = run_command(f"pip install {version}")
        
        if success:
            # Test if it actually works
            test_success = test_directml_import()
            if test_success:
                print(f"✓ Successfully installed and tested {version}")
                return True
            else:
                print(f"⚠️ {version} installed but failed import test")
        else:
            print(f"❌ Failed to install {version}: {stderr}")
    
    print("❌ Could not install a working version of DirectML")
    return False


def test_directml_import():
    """Test if DirectML can be imported and used."""
    try:
        import torch_directml
        # Try to check availability (this is where the error usually occurs)
        available = torch_directml.is_available()
        return True
    except Exception as e:
        print(f"DirectML import test failed: {e}")
        return False


def main():
    """Main installation function."""
    print("DirectML Installation Helper")
    print("=" * 40)
    
    # Check system requirements
    if not check_system_requirements():
        print("\n❌ System requirements not met")
        return
    
    # Install DirectML
    if install_directml():
        print("\n✓ DirectML installation completed successfully!")
        print("\nYou can now use DirectML in your RLGym PPO training:")
        print("  device='auto'      # Auto-select best device")
        print("  device='directml'  # Force DirectML")
        print("  device='dml'       # Force DirectML (short form)")
        
        print("\nRun 'python test_directml.py' to test your installation.")
    else:
        print("\n❌ DirectML installation failed")
        print("\nYour system will fall back to CPU or CUDA (if available).")
        print("You can still use RLGym PPO with device='auto' or device='cpu'.")
        
        print("\nTroubleshooting:")
        print("1. Check that you have a DirectX 12 compatible GPU")
        print("2. Update your GPU drivers")
        print("3. Ensure Windows 10 1903+ or Windows 11")
        print("4. Try manual installation: pip install torch-directml==0.2.3.dev240529")


if __name__ == "__main__":
    main()
