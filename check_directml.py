"""
Simple DirectML diagnostic script
"""

import sys
import platform

print("=== System Information ===")
print(f"Python version: {sys.version}")
print(f"Platform: {platform.platform()}")
print(f"Windows version: {platform.win32_ver()}")
print()

print("=== Checking torch-directml installation ===")
try:
    import torch_directml
    print("✓ torch-directml is installed")
    print(f"torch-directml version: {torch_directml.__version__}")
    
    print("\n=== Checking DirectML availability ===")
    try:
        available = torch_directml.is_available()
        print(f"DirectML available: {available}")
        
        if available:
            device_count = torch_directml.device_count()
            print(f"DirectML device count: {device_count}")
            
            for i in range(device_count):
                device_name = torch_directml.device_name(i)
                print(f"Device {i}: {device_name}")
                
            # Try to get a device
            device = torch_directml.device()
            print(f"Default DirectML device: {device}")
            
        else:
            print("DirectML is not available on this system")
            print("Possible reasons:")
            print("- Windows version too old (need Windows 10 1903+ or Windows 11)")
            print("- No DirectX 12 compatible GPU")
            print("- GPU drivers need updating")
            
    except Exception as e:
        print(f"Error checking DirectML availability: {e}")
        
except ImportError as e:
    print(f"✗ torch-directml is not installed: {e}")
    print("\nTo install torch-directml, run:")
    print("pip install torch-directml")

print("\n=== Checking PyTorch ===")
try:
    import torch
    print(f"✓ PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device count: {torch.cuda.device_count()}")
        print(f"CUDA device name: {torch.cuda.get_device_name()}")
except ImportError:
    print("✗ PyTorch is not installed")

print("\n=== DirectX Information ===")
print("To check DirectX 12 support:")
print("1. Press Win+R, type 'dxdiag', press Enter")
print("2. Look for 'DirectX Version' - should be DirectX 12 or higher")
print("3. Check the 'Display' tab for your GPU information")
