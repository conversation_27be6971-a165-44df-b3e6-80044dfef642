"""
File: directml_utils.py
Author: Augment Agent

Description:
    Utility functions for DirectML device detection and initialization.
    Provides fallback logic and device management for DirectML support.
"""

import torch
import warnings
from typing import Optional, Union


def is_directml_available() -> bool:
    """
    Check if DirectML is available on the current system.

    Returns:
        bool: True if DirectML is available, False otherwise.
    """
    try:
        import torch_directml
        # Try to actually use the library to check if it works
        return torch_directml.is_available()
    except ImportError:
        return False
    except (TypeError, AttributeError, Exception) as e:
        # Handle known issues with torch-directml versions
        warnings.warn(f"DirectML import failed: {e}. This may be due to version compatibility issues.", UserWarning)
        return False


def get_directml_device() -> Optional[torch.device]:
    """
    Get the DirectML device if available.

    Returns:
        torch.device or None: DirectML device if available, None otherwise.
    """
    try:
        import torch_directml
        if torch_directml.is_available():
            return torch_directml.device()
        return None
    except ImportError:
        return None
    except (TypeError, AttributeError, Exception):
        return None


def get_directml_device_count() -> int:
    """
    Get the number of DirectML devices available.

    Returns:
        int: Number of DirectML devices available.
    """
    try:
        import torch_directml
        if torch_directml.is_available():
            return torch_directml.device_count()
        return 0
    except ImportError:
        return 0
    except (TypeError, AttributeError, Exception):
        return 0


def get_directml_device_name(device_index: int = 0) -> Optional[str]:
    """
    Get the name of a DirectML device.

    Args:
        device_index (int): Index of the device to query.

    Returns:
        str or None: Device name if available, None otherwise.
    """
    try:
        import torch_directml
        if torch_directml.is_available() and device_index < torch_directml.device_count():
            return torch_directml.device_name(device_index)
        return None
    except ImportError:
        return None
    except (TypeError, AttributeError, Exception):
        return None


def resolve_device(device: Union[str, torch.device]) -> torch.device:
    """
    Resolve a device string or torch.device to the appropriate device.
    Handles DirectML device resolution with fallback logic.
    
    Args:
        device (str or torch.device): Device specification.
        
    Returns:
        torch.device: Resolved device.
    """
    if isinstance(device, torch.device):
        return device
    
    device_str = str(device).lower().strip()
    
    # Handle DirectML devices
    if device_str in {"directml", "dml"}:
        directml_device = get_directml_device()
        if directml_device is not None:
            print(f"Using DirectML device: {get_directml_device_name()}")
            return directml_device
        else:
            warnings.warn(
                "DirectML requested but not available. Falling back to CUDA if available, otherwise CPU.",
                UserWarning
            )
            if torch.cuda.is_available():
                print("DirectML not available, falling back to CUDA")
                return torch.device("cuda:0")
            else:
                print("DirectML and CUDA not available, falling back to CPU")
                return torch.device("cpu")
    
    # Handle auto device selection with DirectML priority
    elif device_str in {"auto", "gpu"}:
        # Priority: DirectML > CUDA > CPU
        directml_device = get_directml_device()
        if directml_device is not None:
            print(f"Auto-selected DirectML device: {get_directml_device_name()}")
            return directml_device
        elif torch.cuda.is_available():
            print("Auto-selected CUDA device")
            return torch.device("cuda:0")
        else:
            print("Auto-selected CPU device")
            return torch.device("cpu")
    
    # Handle CUDA devices
    elif device_str.startswith("cuda"):
        if torch.cuda.is_available():
            return torch.device(device_str)
        else:
            warnings.warn(
                f"CUDA device '{device_str}' requested but CUDA not available. Falling back to CPU.",
                UserWarning
            )
            return torch.device("cpu")
    
    # Handle CPU
    elif device_str == "cpu":
        return torch.device("cpu")
    
    # Handle unknown device strings
    else:
        warnings.warn(
            f"Unknown device '{device_str}'. Falling back to auto device selection.",
            UserWarning
        )
        return resolve_device("auto")


def setup_directml_optimizations():
    """
    Setup DirectML-specific optimizations if DirectML is being used.
    """
    try:
        import torch_directml
        if torch_directml.is_available():
            # DirectML optimizations are automatically handled by the library
            print("DirectML optimizations enabled")
    except ImportError:
        pass
    except (TypeError, AttributeError, Exception) as e:
        # Silently handle DirectML setup issues
        pass


def print_device_info():
    """
    Print information about available devices.
    """
    print("=== Device Information ===")
    
    # DirectML info
    if is_directml_available():
        device_count = get_directml_device_count()
        print(f"DirectML: Available ({device_count} device(s))")
        for i in range(device_count):
            device_name = get_directml_device_name(i)
            print(f"  Device {i}: {device_name}")
    else:
        print("DirectML: Not available")
    
    # CUDA info
    if torch.cuda.is_available():
        device_count = torch.cuda.device_count()
        print(f"CUDA: Available ({device_count} device(s))")
        for i in range(device_count):
            device_name = torch.cuda.get_device_name(i)
            print(f"  Device {i}: {device_name}")
    else:
        print("CUDA: Not available")
    
    # CPU info
    print("CPU: Available")
    
    print("==========================")


def get_recommended_device() -> torch.device:
    """
    Get the recommended device based on availability and performance.
    
    Returns:
        torch.device: Recommended device.
    """
    return resolve_device("auto")
