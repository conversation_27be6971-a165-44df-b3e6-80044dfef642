"""
RLGym PPO Example with DirectML Support

This example demonstrates how to use R<PERSON><PERSON><PERSON> PPO with DirectML for GPU acceleration on Windows.

Device Options:
- "auto": Automatically selects the best available device (DirectML > CUDA > CPU)
- "directml" or "dml": Explicitly use DirectML (falls back to CUDA/CPU if not available)
- "cuda" or "cuda:0": Use CUDA GPU (if available)
- "cpu": Use CPU only

DirectML enables GPU acceleration on Windows systems with DirectX 12 compatible GPUs,
including AMD, Intel, and NVIDIA GPUs.
"""

import numpy as np
from rlgym_sim.utils.gamestates import GameState
from lookup_act import LookupAction
from rlgym_ppo.util import MetricsLogger
from state_setters import ProbabilisticStateSetter, DribblingStateSetter
from customreward import KickoffProximityReward, ZeroSumReward, SwiftGroundDribbleReward, AirTouchReward, CradleFlickReward, LemTouchBallReward, RetreatReward, DistanceReward, AerialDistanceReward, InAirReward, Tweaked<PERSON>ouch<PERSON>el<PERSON>hange, CradleReward, GroundedReward, GroundDribbleReward, JumpTouchReward
from rlgym_sim.utils.reward_functions import CombinedReward
from rlgym_sim.utils.reward_functions.common_rewards import (
    VelocityBallToGoalReward, VelocityPlayerToBallReward, EventReward, FaceBallReward, SaveBoostReward, TouchBallReward, LiuDistanceBallToGoalReward, 
    AlignBallGoal
)
from rlgym_sim.utils.terminal_conditions.common_conditions import NoTouchTimeoutCondition, GoalScoredCondition
from rlgym_sim.utils.state_setters import RandomState, DefaultState
from obs import AdvancedObsPadder
# Add custom LogCombinedReward
from customreward import LogCombinedReward, GoodVelocityPlayerToBallReward, FlatSpeedReward

g_combined_reward = None  # type: LogCombinedReward

class ExampleLogger(MetricsLogger):
    def _collect_metrics(self, game_state: GameState) -> list:
        # Collect metrics including previous rewards
        metrics = [game_state.players[0].car_data.linear_velocity,
                   game_state.players[0].car_data.rotation_mtx(),
                   game_state.orange_score]
        if g_combined_reward and g_combined_reward.prev_rewards:
            metrics.append(g_combined_reward.prev_rewards)
        return metrics

    def _report_metrics(self, collected_metrics, wandb_run, cumulative_timesteps):
        avg_linvel = np.zeros(3)
        avg_rewards = np.zeros(len(g_combined_reward.reward_functions)) if g_combined_reward else np.zeros(0)
        
        for metric_array in collected_metrics:
            p0_linear_velocity = metric_array[0]
            avg_linvel += p0_linear_velocity
            if g_combined_reward and len(metric_array) > 3:  # Ensure prev_rewards is available
                avg_rewards += metric_array[-1]
        
        avg_linvel /= len(collected_metrics)
        if g_combined_reward:
            avg_rewards /= len(collected_metrics)
        
        report = {"x_vel": avg_linvel[0],
                  "y_vel": avg_linvel[1],
                  "z_vel": avg_linvel[2],
                  "Cumulative Timesteps": cumulative_timesteps}
        
        # Add reward metrics
        if g_combined_reward:
            for i in range(len(g_combined_reward.reward_functions)):
                report["RW " + g_combined_reward.reward_functions[i].__class__.__name__] = avg_rewards[i]
        
        wandb_run.log(report)

def build_rocketsim_env():
    import rlgym_sim
    from customreward import LogCombinedReward


    spawn_opponents = True
    team_size = 1
    game_tick_rate = 120
    tick_skip = 8
    timeout_seconds = 10
    timeout_ticks = int(round(timeout_seconds * game_tick_rate / tick_skip))

    action_parser = LookupAction()
    terminal_conditions = [NoTouchTimeoutCondition(timeout_ticks), GoalScoredCondition()]

    reward_fn = LogCombinedReward.from_zipped(

        (EventReward(boost_pickup=0.13, touch=5), 65),
        (GoodVelocityPlayerToBallReward(), 10),
        (FaceBallReward(), 1),
        (InAirReward(), 0.15),

    )
    global g_combined_reward
    g_combined_reward = reward_fn


    obs_builder = AdvancedObsPadder()

    env = rlgym_sim.make(tick_skip=tick_skip,
                         team_size=team_size,
                         spawn_opponents=spawn_opponents,
                         terminal_conditions=terminal_conditions,
                         reward_fn=reward_fn,
                         obs_builder=obs_builder,
                         action_parser=action_parser
                         )

    import rocketsimvis_rlgym_sim_client as rsv
    type(env).render = lambda self: rsv.send_state_to_rocketsimvis(self._prev_state)
    
    return env

if __name__ == "__main__":
    from rlgym_ppo import Learner
    metrics_logger = ExampleLogger()

    # Optimized for Ryzen 7 5700X (8 cores/16 threads)
    n_proc = 16  # Good for your 16 threads

    
    min_inference_size = max(1, int(round(n_proc * 0.8)))

    learner = Learner(build_rocketsim_env,
                      n_proc=n_proc,
                      min_inference_size=min_inference_size,
                      metrics_logger=metrics_logger,
                      ppo_batch_size=100_000,      
                      ts_per_iteration=100_000,    
                      exp_buffer_size=300_000,     
                      ppo_minibatch_size=25_000,   
                      ppo_ent_coef=0.01,
                      ppo_epochs=3,                
                      standardize_returns=True,
                      standardize_obs=False,
                      save_every_ts=10_000_000,    
                      timestep_limit=100_000_000_000,
                      log_to_wandb=True,
                      policy_layer_sizes=(1024, 512, 512),    
                      critic_layer_sizes=(1024, 512, 512),  
                      device="dml",  # Options: "auto", "directml", "dml", "cuda", "cpu", "cuda:0", etc.
                      render=True,
                      render_delay=0.042,
                      policy_lr=3e-4,              
                      critic_lr=3e-4,              
                      wandb_run_name="Transfer Learning Example",)
    
    build_rocketsim_env()  

    learner.learn()
