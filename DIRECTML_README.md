# DirectML Support for RLGym PPO

This implementation adds DirectML support to RLGym PPO, enabling GPU acceleration on Windows systems with DirectX 12 compatible GPUs, including AMD, Intel, and NVIDIA GPUs.

## What is DirectML?

DirectML is a low-level API for machine learning that provides GPU acceleration across different hardware vendors on Windows. It allows PyTorch to run on any DirectX 12 compatible GPU, making it an excellent choice for systems where CUDA is not available or optimal.

## Benefits

- **Broader GPU Support**: Works with AMD, Intel, and NVIDIA GPUs
- **Windows Optimized**: Native Windows DirectX 12 integration
- **Automatic Fallback**: Gracefully falls back to CUDA or CPU if DirectML is unavailable
- **Easy Integration**: Drop-in replacement for existing device specifications

## Installation

### Prerequisites

1. Windows 10 version 1903 (Build 18362) or newer
2. DirectX 12 compatible GPU
3. Python 3.7 or newer

### Install Dependencies

First, install the base requirements:

```bash
pip install -r requirements.txt
```

Then, install DirectML support separately (optional):

```bash
# Option 1: Install with extras
pip install -e .[directml]

# Option 2: Install manually
pip install torch-directml

# Option 3: Install specific compatible version
pip install torch-directml==0.2.3.dev240529
```

**Note**: DirectML has specific version compatibility requirements with PyTorch. If you encounter import errors, try different versions or check the [torch-directml releases](https://pypi.org/project/torch-directml/#history) for compatibility information.

## Usage

### Basic Usage

Simply change the `device` parameter in your RLGym PPO Learner:

```python
from rlgym_ppo import Learner

learner = Learner(
    build_env_function,
    device="directml",  # Use DirectML
    # ... other parameters
)
```

### Device Options

The following device options are supported:

- `"auto"`: Automatically selects the best available device (DirectML > CUDA > CPU)
- `"directml"` or `"dml"`: Explicitly use DirectML
- `"cuda"` or `"cuda:0"`: Use CUDA GPU (if available)
- `"cpu"`: Use CPU only
- `"gpu"`: Alias for "auto"

### Example

```python
# Automatic device selection (recommended)
learner = Learner(build_env, device="auto")

# Explicit DirectML usage
learner = Learner(build_env, device="directml")

# Fallback to CUDA if DirectML unavailable
learner = Learner(build_env, device="dml")
```

## Testing

Run the test script to verify DirectML functionality:

```bash
python test_directml.py
```

This will:
- Check DirectML availability
- Test device detection and resolution
- Perform tensor operations benchmarks
- Test neural network functionality
- Verify RLGym PPO component compatibility

## Performance Considerations

### DirectML vs CUDA vs CPU

- **DirectML**: Good performance on all DirectX 12 GPUs, especially beneficial for AMD/Intel GPUs
- **CUDA**: Often fastest on NVIDIA GPUs, but only works with NVIDIA hardware
- **CPU**: Slowest but most compatible

### Optimization Tips

1. **Use "auto" device selection**: Let the system choose the best available device
2. **Monitor memory usage**: DirectML may have different memory characteristics than CUDA
3. **Batch size tuning**: Optimal batch sizes may differ between DirectML and CUDA

## Troubleshooting

### DirectML Not Available

If DirectML is not available, check:

1. **Windows Version**: Ensure Windows 10 1903+ or Windows 11
2. **GPU Compatibility**: Verify DirectX 12 support with `dxdiag`
3. **Driver Updates**: Update GPU drivers to latest version
4. **Installation**: Try different torch-directml versions:
   ```bash
   pip uninstall torch-directml
   pip install torch-directml==0.2.3.dev240529
   ```
5. **Version Compatibility**: Check PyTorch and torch-directml compatibility
6. **Import Errors**: If you get `TypeError: 'staticmethod' object is not callable`, try a different torch-directml version

### Performance Issues

If DirectML performance is poor:

1. **Update Drivers**: Ensure latest GPU drivers
2. **Check GPU Usage**: Monitor GPU utilization during training
3. **Adjust Batch Size**: Try different batch sizes
4. **Compare Devices**: Use the test script to compare performance

### Memory Errors

If you encounter memory errors:

1. **Reduce Batch Size**: Lower `ppo_batch_size` and `ppo_minibatch_size`
2. **Reduce Network Size**: Smaller `policy_layer_sizes` and `critic_layer_sizes`
3. **Monitor Memory**: Check GPU memory usage

## Implementation Details

### Device Resolution Logic

The device resolution follows this priority:

1. **DirectML**: If available and requested/auto
2. **CUDA**: If available and DirectML not available/requested
3. **CPU**: Fallback option

### Automatic Optimizations

The implementation automatically:

- Enables DirectML tiled resources for better memory management
- Sets up CUDA optimizations when using CUDA devices
- Provides detailed device information during initialization

### Files Modified

- `requirements.txt`: Added torch-directml dependency
- `setup.py`: Added torch-directml to install_requires
- `rlgym_ppo/learner.py`: Updated device handling logic
- `rlgym_ppo/util/directml_utils.py`: New DirectML utility functions
- `example.py`: Updated to demonstrate DirectML usage

## Compatibility

This implementation is backward compatible with existing code. If DirectML is not available, the system will automatically fall back to CUDA or CPU as before.

## Support

For DirectML-specific issues:
- Check the [torch-directml GitHub repository](https://github.com/microsoft/DirectML)
- Review Microsoft's DirectML documentation

For RLGym PPO issues:
- Check the original RLGym PPO repository
- Ensure your environment setup is correct

## License

This DirectML implementation follows the same license as the original RLGym PPO project.
