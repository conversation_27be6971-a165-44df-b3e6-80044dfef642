2025-07-20 23:46:37,400 INFO    MainThread:2580 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-20 23:46:37,401 INFO    MainThread:2580 [wandb_setup.py:_flush():80] Configure stats pid to 2580
2025-07-20 23:46:37,401 INFO    MainThread:2580 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-07-20 23:46:37,401 INFO    MainThread:2580 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\settings
2025-07-20 23:46:37,401 INFO    MainThread:2580 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-20 23:46:37,401 INFO    MainThread:2580 [wandb_init.py:setup_run_log_directory():703] Logging user logs to C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\run-20250720_234637-5m89fz45\logs\debug.log
2025-07-20 23:46:37,403 INFO    MainThread:2580 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\run-20250720_234637-5m89fz45\logs\debug-internal.log
2025-07-20 23:46:37,403 INFO    MainThread:2580 [wandb_init.py:init():830] calling init triggers
2025-07-20 23:46:37,403 INFO    MainThread:2580 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'n_proc': 16, 'min_inference_size': 13, 'timestep_limit': 100000000000, 'exp_buffer_size': 300000, 'ts_per_iteration': 100000, 'standardize_returns': True, 'standardize_obs': False, 'policy_layer_sizes': (1024, 512, 512), 'critic_layer_sizes': (1024, 512, 512), 'ppo_epochs': 3, 'ppo_batch_size': 100000, 'ppo_minibatch_size': 25000, 'ppo_ent_coef': 0.01, 'ppo_clip_range': 0.1, 'gae_lambda': 0.95, 'gae_gamma': 0.99, 'policy_lr': 0.0003, 'critic_lr': 0.0003, 'shm_buffer_size': 8192, '_wandb': {}}
2025-07-20 23:46:37,403 INFO    MainThread:2580 [wandb_init.py:init():871] starting backend
2025-07-20 23:46:37,629 INFO    MainThread:2580 [wandb_init.py:init():874] sending inform_init request
2025-07-20 23:46:37,641 INFO    MainThread:2580 [wandb_init.py:init():882] backend started and connected
2025-07-20 23:46:37,641 INFO    MainThread:2580 [wandb_init.py:init():953] updated telemetry
2025-07-20 23:46:37,644 INFO    MainThread:2580 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-20 23:46:38,105 INFO    MainThread:2580 [wandb_init.py:init():1029] starting run threads in backend
2025-07-20 23:46:38,157 INFO    MainThread:2580 [wandb_run.py:_console_start():2458] atexit reg
2025-07-20 23:46:38,157 INFO    MainThread:2580 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-20 23:46:38,157 INFO    MainThread:2580 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-20 23:46:38,157 INFO    MainThread:2580 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-20 23:46:38,159 INFO    MainThread:2580 [wandb_init.py:init():1075] run started, returning control to user process
