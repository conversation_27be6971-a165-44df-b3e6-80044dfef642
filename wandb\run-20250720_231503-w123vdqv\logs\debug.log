2025-07-20 23:15:03,988 INFO    MainThread:31736 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-20 23:15:03,988 INFO    MainThread:31736 [wandb_setup.py:_flush():80] Configure stats pid to 31736
2025-07-20 23:15:03,988 INFO    MainThread:31736 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-07-20 23:15:03,988 INFO    MainThread:31736 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\settings
2025-07-20 23:15:03,988 INFO    MainThread:31736 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-20 23:15:03,988 INFO    MainThread:31736 [wandb_init.py:setup_run_log_directory():703] Logging user logs to C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\run-20250720_231503-w123vdqv\logs\debug.log
2025-07-20 23:15:03,989 INFO    MainThread:31736 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\run-20250720_231503-w123vdqv\logs\debug-internal.log
2025-07-20 23:15:03,989 INFO    MainThread:31736 [wandb_init.py:init():830] calling init triggers
2025-07-20 23:15:03,989 INFO    MainThread:31736 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'n_proc': 32, 'min_inference_size': 29, 'timestep_limit': 100000000000, 'exp_buffer_size': 150000, 'ts_per_iteration': 50000, 'standardize_returns': True, 'standardize_obs': False, 'policy_layer_sizes': (2048, 1024, 1024, 1024), 'critic_layer_sizes': (2048, 1024, 1024, 1024), 'ppo_epochs': 2, 'ppo_batch_size': 50000, 'ppo_minibatch_size': 25000, 'ppo_ent_coef': 0.01, 'ppo_clip_range': 0.1, 'gae_lambda': 0.95, 'gae_gamma': 0.99, 'policy_lr': 0.0002, 'critic_lr': 0.0002, 'shm_buffer_size': 8192, '_wandb': {}}
2025-07-20 23:15:03,989 INFO    MainThread:31736 [wandb_init.py:init():871] starting backend
2025-07-20 23:15:04,267 INFO    MainThread:31736 [wandb_init.py:init():874] sending inform_init request
2025-07-20 23:15:04,279 INFO    MainThread:31736 [wandb_init.py:init():882] backend started and connected
2025-07-20 23:15:04,280 INFO    MainThread:31736 [wandb_init.py:init():953] updated telemetry
2025-07-20 23:15:04,282 INFO    MainThread:31736 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-20 23:15:04,817 INFO    MainThread:31736 [wandb_init.py:init():1029] starting run threads in backend
2025-07-20 23:15:04,887 INFO    MainThread:31736 [wandb_run.py:_console_start():2458] atexit reg
2025-07-20 23:15:04,887 INFO    MainThread:31736 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-20 23:15:04,887 INFO    MainThread:31736 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-20 23:15:04,887 INFO    MainThread:31736 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-20 23:15:04,890 INFO    MainThread:31736 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-20 23:15:20,942 WARNING MsgRouterThr:31736 [router.py:message_loop():63] [no run ID] message_loop has been closed
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 204, in _read_packet_bytes
    data = self._sock.recv(self._bufsize)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\router_sock.py", line 27, in _read_message
    return self._sock_client.read_server_response(timeout=1)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 231, in read_server_response
    data = self._read_packet_bytes(timeout=timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 208, in _read_packet_bytes
    raise SockClientClosedError from e
wandb.sdk.lib.sock_client.SockClientClosedError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\router.py", line 56, in message_loop
    msg = self._read_message()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\router_sock.py", line 29, in _read_message
    raise MessageRouterClosedError from e
wandb.sdk.interface.router.MessageRouterClosedError
2025-07-20 23:15:20,944 INFO    MsgRouterThr:31736 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
