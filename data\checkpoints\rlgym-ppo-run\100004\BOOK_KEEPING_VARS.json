{"cumulative_timesteps": 100004, "cumulative_model_updates": 0, "policy_average_reward": 156.57677187502642, "epoch": 0, "ts_since_last_save": 0, "reward_running_stats": {"mean": [68.4097335849726], "var": [581258.0781425136], "shape": [1], "count": 300}, "wandb_run_id": "q5cbkgu8", "wandb_project": "rlgym-ppo", "wandb_entity": "xzskullyxz-rlgym", "wandb_group": "unnamed-runs", "wandb_config": {"n_proc": 8, "min_inference_size": 7, "timestep_limit": 100000000000, "exp_buffer_size": 150000, "ts_per_iteration": 50000, "standardize_returns": true, "standardize_obs": false, "policy_layer_sizes": [2048, 1024, 1024, 1024], "critic_layer_sizes": [2048, 1024, 1024, 1024], "ppo_epochs": 2, "ppo_batch_size": 50000, "ppo_minibatch_size": 25000, "ppo_ent_coef": 0.01, "ppo_clip_range": 0.1, "gae_lambda": 0.95, "gae_gamma": 0.99, "policy_lr": 0.0002, "critic_lr": 0.0002, "shm_buffer_size": 8192}}