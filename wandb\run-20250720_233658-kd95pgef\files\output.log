Created new wandb run! kd95p<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\torch\nn\utils\clip_grad.py:67: UserWarning: The operator 'aten::_foreach_norm.Scalar' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  norms.extend(torch._foreach_norm(device_grads, norm_type))

--------BEGIN ITERATION REPORT--------
Policy Reward: nan
Policy Entropy: 4.49957
Value Function Loss: nan

Mean KL Divergence: 0.00004
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.63924
Value Function Update Magnitude: 0.63937

Collected Steps per Second: 2,974.08649
Overall Steps per Second: 962.50884

Timestep Collection Time: 0.33624
Timestep Consumption Time: 0.70271
PPO Batch Consumption Time: 0.31912
Total Iteration Time: 1.03895

Cumulative Model Updates: 2
Cumulative Timesteps: 1,000

Timesteps Collected: 1,000
--------END ITERATION REPORT--------




LEARNING LOOP ENCOUNTERED AN ERROR
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 229, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 305, in _learn
    if "cuda" in self.device:
TypeError: argument of type 'torch.device' is not iterable

Saving checkpoint 1000...
Checkpoint 1000 saved!
