"""
DirectML Test Script for RLGym PPO

This script tests DirectML functionality and device detection.
Run this script to verify that DirectML is working correctly with your setup.
"""

import torch
import numpy as np
import time
from rlgym_ppo.util.directml_utils import (
    is_directml_available,
    get_directml_device,
    get_directml_device_count,
    get_directml_device_name,
    resolve_device,
    print_device_info,
    get_recommended_device
)


def test_device_detection():
    """Test device detection functionality."""
    print("=== Testing Device Detection ===")
    
    print(f"DirectML available: {is_directml_available()}")
    print(f"DirectML device count: {get_directml_device_count()}")
    
    directml_device = get_directml_device()
    if directml_device:
        print(f"DirectML device: {directml_device}")
        print(f"DirectML device name: {get_directml_device_name()}")
    else:
        print("DirectML device: None")
    
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device count: {torch.cuda.device_count()}")
        print(f"CUDA device name: {torch.cuda.get_device_name()}")
    
    print()


def test_device_resolution():
    """Test device resolution functionality."""
    print("=== Testing Device Resolution ===")
    
    test_devices = ["auto", "directml", "dml", "cuda", "cpu", "gpu"]
    
    for device_str in test_devices:
        try:
            resolved = resolve_device(device_str)
            print(f"'{device_str}' -> {resolved}")
        except Exception as e:
            print(f"'{device_str}' -> Error: {e}")
    
    print()


def test_tensor_operations(device):
    """Test basic tensor operations on the specified device."""
    print(f"=== Testing Tensor Operations on {device} ===")
    
    try:
        # Create test tensors
        a = torch.randn(1000, 1000, device=device)
        b = torch.randn(1000, 1000, device=device)
        
        # Test basic operations
        start_time = time.time()
        c = torch.matmul(a, b)
        matmul_time = time.time() - start_time
        
        start_time = time.time()
        d = a + b
        add_time = time.time() - start_time
        
        start_time = time.time()
        e = torch.relu(c)
        relu_time = time.time() - start_time
        
        print(f"Matrix multiplication (1000x1000): {matmul_time:.4f}s")
        print(f"Addition (1000x1000): {add_time:.4f}s")
        print(f"ReLU (1000x1000): {relu_time:.4f}s")
        print(f"Result tensor shape: {c.shape}")
        print(f"Result tensor device: {c.device}")
        print("✓ Tensor operations successful")
        
    except Exception as e:
        print(f"✗ Tensor operations failed: {e}")
    
    print()


def test_neural_network(device):
    """Test a simple neural network on the specified device."""
    print(f"=== Testing Neural Network on {device} ===")
    
    try:
        # Create a simple neural network
        model = torch.nn.Sequential(
            torch.nn.Linear(100, 256),
            torch.nn.ReLU(),
            torch.nn.Linear(256, 128),
            torch.nn.ReLU(),
            torch.nn.Linear(128, 10)
        ).to(device)
        
        # Create test data
        x = torch.randn(32, 100, device=device)
        target = torch.randint(0, 10, (32,), device=device)
        
        # Test forward pass
        start_time = time.time()
        output = model(x)
        forward_time = time.time() - start_time
        
        # Test loss computation
        criterion = torch.nn.CrossEntropyLoss()
        loss = criterion(output, target)
        
        # Test backward pass
        start_time = time.time()
        loss.backward()
        backward_time = time.time() - start_time
        
        print(f"Forward pass time: {forward_time:.4f}s")
        print(f"Backward pass time: {backward_time:.4f}s")
        print(f"Output shape: {output.shape}")
        print(f"Loss: {loss.item():.4f}")
        print("✓ Neural network test successful")
        
    except Exception as e:
        print(f"✗ Neural network test failed: {e}")
    
    print()


def test_rlgym_ppo_components():
    """Test RLGym PPO components with DirectML."""
    print("=== Testing RLGym PPO Components ===")
    
    try:
        from rlgym_ppo.ppo import ContinuousPolicy, ValueEstimator
        
        device = get_recommended_device()
        print(f"Using device: {device}")
        
        # Test policy network
        policy = ContinuousPolicy(
            input_shape=100,
            output_shape=16,  # 8 actions * 2 (mean + std)
            layer_sizes=(256, 128, 64),
            device=device
        )
        
        # Test value network
        value_net = ValueEstimator(
            input_shape=100,
            layer_sizes=(256, 128, 64),
            device=device
        )
        
        # Test forward passes
        obs = torch.randn(32, 100, device=device)
        
        policy_output = policy(obs)
        value_output = value_net(obs)
        
        print(f"Policy output shape: {policy_output.shape}")
        print(f"Value output shape: {value_output.shape}")
        print("✓ RLGym PPO components test successful")
        
    except Exception as e:
        print(f"✗ RLGym PPO components test failed: {e}")
    
    print()


def main():
    """Main test function."""
    print("DirectML Test Script for RLGym PPO")
    print("=" * 50)
    
    # Print device information
    print_device_info()
    print()
    
    # Test device detection
    test_device_detection()
    
    # Test device resolution
    test_device_resolution()
    
    # Get recommended device for testing
    recommended_device = get_recommended_device()
    print(f"Recommended device: {recommended_device}")
    print()
    
    # Test tensor operations
    test_tensor_operations(recommended_device)
    
    # Test neural network
    test_neural_network(recommended_device)
    
    # Test RLGym PPO components
    test_rlgym_ppo_components()
    
    print("=" * 50)
    print("DirectML test completed!")
    
    # Performance comparison if multiple devices available
    available_devices = []
    if is_directml_available():
        available_devices.append(get_directml_device())
    if torch.cuda.is_available():
        available_devices.append(torch.device("cuda:0"))
    available_devices.append(torch.device("cpu"))
    
    if len(available_devices) > 1:
        print("\n=== Performance Comparison ===")
        for device in available_devices:
            print(f"\nTesting performance on {device}:")
            start_time = time.time()
            test_tensor_operations(device)
            total_time = time.time() - start_time
            print(f"Total time for {device}: {total_time:.4f}s")


if __name__ == "__main__":
    main()
