Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\ppo\discrete_policy.py:59: UserWarning: The operator 'aten::multinomial' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  action = torch.multinomial(probs, 1, True)



LEARNING LOOP ENCOUNTERED AN ERROR
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 229, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 274, in _learn
    ppo_report = self.ppo_learner.learn(self.experience_buffer)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\ppo\ppo_learner.py", line 182, in learn
    ppo_loss.backward()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\_tensor.py", line 522, in backward
    torch.autograd.backward(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\autograd\__init__.py", line 266, in backward
    Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
RuntimeError: DirectML scatter doesn't allow partially modified dimensions. Please update the dimension so that the indices and input only differ in the provided dimension.

Saving checkpoint 100004...
Checkpoint 100004 saved!
