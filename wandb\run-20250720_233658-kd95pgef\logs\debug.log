2025-07-20 23:36:58,932 INFO    MainThread:35868 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-20 23:36:58,932 INFO    MainThread:35868 [wandb_setup.py:_flush():80] Configure stats pid to 35868
2025-07-20 23:36:58,932 INFO    MainThread:35868 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-07-20 23:36:58,932 INFO    MainThread:35868 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\settings
2025-07-20 23:36:58,933 INFO    MainThread:35868 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-20 23:36:58,933 INFO    MainThread:35868 [wandb_init.py:setup_run_log_directory():703] Logging user logs to C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\run-20250720_233658-kd95pgef\logs\debug.log
2025-07-20 23:36:58,933 INFO    MainThread:35868 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\run-20250720_233658-kd95pgef\logs\debug-internal.log
2025-07-20 23:36:58,933 INFO    MainThread:35868 [wandb_init.py:init():830] calling init triggers
2025-07-20 23:36:58,933 INFO    MainThread:35868 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'n_proc': 8, 'min_inference_size': 7, 'timestep_limit': 10000, 'exp_buffer_size': 2000, 'ts_per_iteration': 1000, 'standardize_returns': True, 'standardize_obs': False, 'policy_layer_sizes': (2048, 1024, 1024, 1024), 'critic_layer_sizes': (2048, 1024, 1024, 1024), 'ppo_epochs': 2, 'ppo_batch_size': 1000, 'ppo_minibatch_size': 500, 'ppo_ent_coef': 0.01, 'ppo_clip_range': 0.1, 'gae_lambda': 0.95, 'gae_gamma': 0.99, 'policy_lr': 0.0002, 'critic_lr': 0.0002, 'shm_buffer_size': 8192, '_wandb': {}}
2025-07-20 23:36:58,933 INFO    MainThread:35868 [wandb_init.py:init():871] starting backend
2025-07-20 23:36:59,153 INFO    MainThread:35868 [wandb_init.py:init():874] sending inform_init request
2025-07-20 23:36:59,165 INFO    MainThread:35868 [wandb_init.py:init():882] backend started and connected
2025-07-20 23:36:59,166 INFO    MainThread:35868 [wandb_init.py:init():953] updated telemetry
2025-07-20 23:36:59,168 INFO    MainThread:35868 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-20 23:36:59,589 INFO    MainThread:35868 [wandb_init.py:init():1029] starting run threads in backend
2025-07-20 23:36:59,642 INFO    MainThread:35868 [wandb_run.py:_console_start():2458] atexit reg
2025-07-20 23:36:59,642 INFO    MainThread:35868 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-20 23:36:59,642 INFO    MainThread:35868 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-20 23:36:59,643 INFO    MainThread:35868 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-20 23:36:59,645 INFO    MainThread:35868 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-20 23:37:01,648 INFO    MainThread:35868 [wandb_run.py:_finish():2224] finishing run xzskullyxz-rlgym/rlgym-ppo/kd95pgef
2025-07-20 23:37:01,649 INFO    MainThread:35868 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-07-20 23:37:01,649 INFO    MainThread:35868 [wandb_run.py:_restore():2405] restore
2025-07-20 23:37:01,650 INFO    MainThread:35868 [wandb_run.py:_restore():2411] restore done
2025-07-20 23:37:02,203 INFO    MainThread:35868 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-07-20 23:37:02,203 INFO    MainThread:35868 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-07-20 23:37:02,204 INFO    MainThread:35868 [wandb_run.py:_footer_sync_info():3864] logging synced files
