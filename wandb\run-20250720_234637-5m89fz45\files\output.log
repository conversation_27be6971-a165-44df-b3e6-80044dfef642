Created new wandb run! 5m89fz<PERSON>
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\torch\nn\utils\clip_grad.py:67: UserWarning: The operator 'aten::_foreach_norm.Scalar' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  norms.extend(torch._foreach_norm(device_grads, norm_type))

--------BEGIN ITERATION REPORT--------
Policy Reward: 273.08032
Policy Entropy: 4.49897
Value Function Loss: nan

Mean KL Divergence: 0.00016
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.71071
Value Function Update Magnitude: 0.71986

Collected Steps per Second: 7,618.53871
Overall Steps per Second: 5,741.89319

Timestep Collection Time: 13.12693
Timestep Consumption Time: 4.29033
PPO Batch Consumption Time: 0.49603
Total Iteration Time: 17.41725

Cumulative Model Updates: 3
Cumulative Timesteps: 100,008

Timesteps Collected: 100,008
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 219.48528
Policy Entropy: 4.49711
Value Function Loss: 439.38134

Mean KL Divergence: 0.00094
SB3 Clip Fraction: 0.03212
Policy Update Magnitude: 0.48921
Value Function Update Magnitude: 0.62152

Collected Steps per Second: 7,549.78415
Overall Steps per Second: 6,152.00691

Timestep Collection Time: 13.24568
Timestep Consumption Time: 3.00951
PPO Batch Consumption Time: 0.33466
Total Iteration Time: 16.25518

Cumulative Model Updates: 6
Cumulative Timesteps: 200,010

Timesteps Collected: 100,002
--------END ITERATION REPORT--------
