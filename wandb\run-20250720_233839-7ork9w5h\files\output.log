Created new wandb run! 7ork9w5h
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\torch\nn\utils\clip_grad.py:67: UserWarning: The operator 'aten::_foreach_norm.Scalar' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  norms.extend(torch._foreach_norm(device_grads, norm_type))

--------BEGIN ITERATION REPORT--------
Policy Reward: 222.41268
Policy Entropy: 4.49955
Value Function Loss: nan

Mean KL Divergence: 0.00004
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.69541
Value Function Update Magnitude: 0.68635

Collected Steps per Second: 7,039.58028
Overall Steps per Second: 4,189.41950

Timestep Collection Time: 7.10326
Timestep Consumption Time: 4.83252
PPO Batch Consumption Time: 1.55623
Total Iteration Time: 11.93578

Cumulative Model Updates: 2
Cumulative Timesteps: 50,004

Timesteps Collected: 50,004
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 237.08341
Policy Entropy: 4.49814
Value Function Loss: 485.77389

Mean KL Divergence: 0.00088
SB3 Clip Fraction: 0.02725
Policy Update Magnitude: 0.85173
Value Function Update Magnitude: 1.11511

Collected Steps per Second: 7,238.40567
Overall Steps per Second: 4,761.21636

Timestep Collection Time: 6.90926
Timestep Consumption Time: 3.59478
PPO Batch Consumption Time: 0.64588
Total Iteration Time: 10.50404

Cumulative Model Updates: 6
Cumulative Timesteps: 100,016

Timesteps Collected: 50,012
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 218.01917
Policy Entropy: 4.49692
Value Function Loss: 295.64243

Mean KL Divergence: 0.00127
SB3 Clip Fraction: 0.04979
Policy Update Magnitude: 0.62387
Value Function Update Magnitude: 1.04477

Collected Steps per Second: 7,191.56158
Overall Steps per Second: 4,742.69428

Timestep Collection Time: 6.95259
Timestep Consumption Time: 3.58994
PPO Batch Consumption Time: 0.64614
Total Iteration Time: 10.54253

Cumulative Model Updates: 10
Cumulative Timesteps: 150,016

Timesteps Collected: 50,000
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 276.43119
Policy Entropy: 4.49601
Value Function Loss: 49.22647

Mean KL Divergence: 0.00098
SB3 Clip Fraction: 0.04002
Policy Update Magnitude: 0.65562
Value Function Update Magnitude: 0.64037

Collected Steps per Second: 6,889.46962
Overall Steps per Second: 4,130.50332

Timestep Collection Time: 7.25919
Timestep Consumption Time: 4.84877
PPO Batch Consumption Time: 0.62272
Total Iteration Time: 12.10797

Cumulative Model Updates: 16
Cumulative Timesteps: 200,028

Timesteps Collected: 50,012
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 211.95053
Policy Entropy: 4.49461
Value Function Loss: 16.35233

Mean KL Divergence: 0.00087
SB3 Clip Fraction: 0.02739
Policy Update Magnitude: 0.45929
Value Function Update Magnitude: 0.90617

Collected Steps per Second: 7,325.43488
Overall Steps per Second: 4,329.27965

Timestep Collection Time: 6.82635
Timestep Consumption Time: 4.72430
PPO Batch Consumption Time: 0.62006
Total Iteration Time: 11.55065

Cumulative Model Updates: 22
Cumulative Timesteps: 250,034

Timesteps Collected: 50,006
--------END ITERATION REPORT--------
